<template>
  <div class="systemSettingBox">
    <div class="systemSettingLayout">
      <div class="settingMain">
        <!-- 账号 -->
        <div class="settingLine">
          <span class="lineTitle">{{ $t("account") }}</span>
          <span class="lineContent">{{ account }}</span>
        </div>

        <!-- 昵称 -->
        <div class="settingLine">
          <span class="lineTitle">{{ $t("nickName") }}</span>
          <el-input
            v-model="nickName"
            :placeholder="$t('inputLengthLimit', { limit: 30 })"
            class="lineContent"
            maxlength="30"
          />
        </div>
      </div>
      <div class="settingFooter">
        <el-button
          :loading="isSaving"
          class="tipSubmitBtn"
          type="primary"
          @click="save()"
        >{{ $t("save") }}</el-button
        >
        <el-button class="tipCancelBtn" type="info" @click="modifyPassword()">{{
          $t("modifyPassword")
        }}</el-button>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :before-close="hideDialog"
      width="600px"
    >
      <template v-if="dialogTitle === $t('modifyPassword')">
        <el-form
          ref="modifyPasswordDialogForm"
          :rules="modifyPasswordDialogForm.rules"
          :model="modifyPasswordDialogForm.data"
          label-width="240px"
          label-position="left"
        >
          <el-form-item :label="$t('oldPassword')" prop="oldPassword">
            <el-input
              v-model="modifyPasswordDialogForm.data.oldPassword"
              :placeholder="$t('passwordTip1')"
              maxlength="18"
              type="password"
              show-password
            />
          </el-form-item>
          <el-form-item :label="$t('newPassword')" prop="newPassword">
            <el-input
              v-model="modifyPasswordDialogForm.data.newPassword"
              :placeholder="$t('inputLengthLimit', { limit: '6-18' })"
              maxlength="18"
              type="password"
              show-password
            />
          </el-form-item>
          <el-form-item :label="$t('checkPassword')" prop="confirmPassword">
            <el-input
              v-model="modifyPasswordDialogForm.data.confirmPassword"
              :placeholder="$t('inputLengthLimit', { limit: '6-18' })"
              maxlength="18"
              type="password"
              show-password
            />
          </el-form-item>
        </el-form>
      </template>
      <span slot="footer" class="dialog-footer">
        <el-button
          :loading="isSubmitting"
          class="tipSubmitBtn"
          @click="submit()"
        >{{ $t("sure") }}</el-button
        >
        <el-button type="info" class="tipCancelBtn" @click="hideDialog()">{{
          $t("cancel")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { removeToken, removeRole } from '@/utils/auth'
import { changePassword, changeName } from '@/api/system'
import autoReloginMixin from '@/mixins/autoRelogin'

export default {
  name: 'SystemSetting',
  mixins: [autoReloginMixin],

  data: function() {
    const validateCheckpassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('pleaseEnterCorrectPwd18')))
      } else if (value !== this.modifyPasswordDialogForm.data.newPassword) {
        callback(new Error(this.$t('passwordNotMatching')))
      } else {
        callback()
      }
    }

    return {
      nickName: '',
      account: '',

      dialogVisible: false,
      dialogTitle: this.$t('modifyPassword'),

      modifyPasswordDialogForm: {
        data: {
          oldPassword: '',
          newPassword: '',
          confirmPassword: ''
        },
        rules: {
          oldPassword: [{
            required: true,
            message: this.$t('pleaseEnterCorrectPwd18'),
            trigger: 'blur',
            min: 6,
            max: 18
          }],
          newPassword: [{
            required: true,
            trigger: 'blur',
            message: this.$t('pleaseEnterCorrectPwd18'),
            min: 6,
            max: 18
          }],
          confirmPassword: [{
            required: true,
            validator: validateCheckpassword,
            trigger: 'blur'
          }]
        }
      },
      isSubmitting: false,
      isSaving: false
    }
  },
  created() {
    // mixin会自动调用handleAutoRelogin()，这里不需要重复调用

    this.nickName = sessionStorage.account_alias
    this.account = sessionStorage.username
  },
  methods: {
    // 重写mixin的回调方法 - 重登成功后执行
    onReloginSuccess(response) {
    },

    // 重写mixin的回调方法 - websocket准备就绪后执行
    onWebsocketReady() {
      console.log('systemSetting页面websocket准备就绪')
      // systemSetting页面不需要特殊的数据加载
    },

    // 保存
    save() {
      this.isSaving = true

      changeName({
        name: this.nickName
      }).then(res => {
        if (res.code === 200) {
          sessionStorage.account_alias = this.nickName
          this.$message.success(this.$t('editSuccess'))

          this.$bus.$emit('refreshUserInfo', this.nickName)
        } else {

        }
      }).catch(err => {

      }).finally(() => {
        this.isSaving = false
      })
    },

    // 修改密码
    modifyPassword() {
      console.log('修改密码')
      this.showDialog()
    },

    // 显示对话框
    showDialog() {
      this.dialogVisible = true
    },

    // 隐藏对话框
    hideDialog() {
      this.dialogVisible = false
      setTimeout(() => {
        this.clearFormData()
      }, 100)
    },

    // 提交
    submit() {
      console.log('确认')
      // 验证表单
      this.$refs['modifyPasswordDialogForm'].validate((valid) => {
        if (!valid) {
          return false
        } else {
          if (this.modifyPasswordDialogForm.data.newPassword === this.modifyPasswordDialogForm.data.oldPassword) {
            return this.$message.error(this.$t('passwordSame'))
          }

          this.isSubmitting = true

          const reqData = {
            password_old: this.$md5(this.modifyPasswordDialogForm.data.oldPassword),
            password_new: this.$md5(this.modifyPasswordDialogForm.data.newPassword)
          }
          changePassword(reqData)
            .then(res => {
              if (res.code === 200) {
                this.hideDialog()
                this.$message.success(this.$t('modifySuccess'))
                this.clearFormData()
                this.$store.commit('SET_TOKEN', '')
                removeToken()
                removeRole()
                this.$router.push({ path: '/login' })
              }
            }).catch(err => {

            }).finally(() => {
              this.isSubmitting = false
            })
        }
      })
    },

    clearFormData() {
      this.modifyPasswordDialogForm.data = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
div,
text {
  color: var(--color-neutral-100);
  font-size: var(--font-size-normal);
}
.systemSettingBox {
  height: calc(100vh - 75px);
  background: var(--color-neutral-700);
  box-sizing: border-box;
  padding-top: 30px;
}
.systemSettingLayout {
  background: var(--color-neutral-600);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 20px;
  border-radius: 5px;
  gap: 40px;
  width: 600px;
  margin: auto;
}
.settingMain {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}
.settingLine {
  display: flex;
  align-items: center;
}
.lineTitle {
  width: 120px;
}
.lineContent {
  flex: 1;
}
.settingButton {
  min-width: 120px;
}
.settingFooter {
  display: flex;
  gap: 12px;
  width: 100%;
  .tipSubmitBtn,
  .tipCancelBtn {
    flex: 1;
  }
}
</style>
