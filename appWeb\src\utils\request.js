import axios from 'axios'
import store from '@/store'
import { removeToken, removeRole } from '@/utils/auth'
import i18n from '@/lang'
import { Message } from 'element-ui'
import tabManager from '@/utils/tabManager'
import router from '@/router'
import reloginManager from '@/utils/reloginManager'
import { encryption, decryption } from '@/utils/jiami'
import Vue from 'vue'

// 处理 errCode401 的重登逻辑
export function handleErrCode401 () {
  console.log('收到 errCode401，尝试重登')

  return reloginManager.startRelogin(() => {
    return new Promise((resolve, reject) => {
      // 获取缓存的  account 和 password
      const cachedAccount = tabManager.getAccount()
      const cachedPasswd = tabManager.getPassword()

      if (!cachedAccount) {
        console.log('没有缓存的  account直接显示错误提示并强制登出')
        Message.error(i18n.t('errCode401'))
        forceLogout()
        reject(new Error('errCode401'))
        return
      }

      console.log('发现缓存的  account，尝试重登')

      const passwd = decryption(cachedPasswd)

      // 使用 WebSocket 进行重登
      if (Vue.prototype.$websocket) {
        // 构造重登数据，参考登录页的重登逻辑
        const post_data = {
          language_type: 0,
          account: cachedAccount,
          passwd: Vue.prototype.$md5(passwd),
          os: 1,
          net: 1, // 默认网络类型
          name: 'wifi', // 默认网络名称
          bundle_id: '**********',
          device_token: Vue.prototype.$websocket.generatedCode(), // 使用时间戳作为设备token
          vid: '0001',
          vkey: 'DcWP670PNfCtPIETQk03lEzbt6qRDRDy'
        }

        // 发送重登请求
        Vue.prototype.$websocket.webSocketSend('cli_miru_login', post_data)

        // 监听重登响应
        Vue.prototype.$bus.$once('cli_miru_login_rsp', response => {
          console.log('errCode401 重登响应:', response)

          if (typeof response === 'number') {
            // 重登失败
            console.log('errCode401 重登失败，显示错误提示并强制登出')
            // Message.error(i18n.t('errCode401'))

            forceLogout()
            reject(new Error('errCode' + response))
            return
          }

          // 重登成功，更新登录状态
          console.log('errCode401 重登成功')

          // 更新 store 和 sessionStorage 中的数据
          store.commit('SET_TOKEN', response.body.sessid)
          sessionStorage.token = response.body.sessid

          if (response.body.account_type !== undefined) {
            sessionStorage.account_type = response.body.account_type
          }
          if (response.body.account) {
            sessionStorage.username = response.body.account
          }
          if (response.body.alias) {
            sessionStorage.account_alias = response.body.alias
          }
          if (response.body.srs_http) {
            sessionStorage.videoUrl = JSON.stringify(response.body.srs_http)
          }

          // 更新 localStorage 中的 sessid 和 account
          tabManager.setSessid(response.body.sessid, response.body.account)

          console.log('errCode401 重登成功，登录状态已更新')
          resolve()
        })
      } else {
        // WebSocket 不可用，直接失败
        console.log('WebSocket 不可用，无法进行重登')
        Message.error(i18n.t('errCode401'))
        forceLogout()
        reject(new Error('errCode401'))
      }
    })
  }).catch((error) => {
    console.log('errCode401 重登失败:', error.message)
    Message.error(i18n.t(error.message))
    forceLogout()
    return Promise.reject(error)
  })
}

// 强制登出函数
function forceLogout () {
  console.log('执行强制登出')
  // 重置重登状态
  reloginManager.reset()

  // 清除用户数据
  store.dispatch('FedLogOut')

  // 跳转到登录页
  if (router.currentRoute.path !== '/login') {
    router.replace('/login')
  }
}

// 创建axios实例
const service = axios.create({
  baseURL: process.env.BASE_API, // api 的 base_url
  timeout: 10000 // request timeout
})

// request拦截器
service.interceptors.request.use(
  config => {
    const reqConfig = { ...config }
    if (reqConfig.data === undefined) reqConfig.data = {}
    reqConfig.data.time = Math.round(new Date().valueOf() / 1000)
    reqConfig.data.auth_token = store.getters.token
    return reqConfig
  },
  error => {
    // Do something with request error
    console.log(error) // for debug
    Promise.reject(error)
  }
)

// respone拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== 200) {
      var tip = ''
      if (res.code === 401) {
        // 尝试重登，重登成功的话就不弹报错消息
        return handleErrCode401().then(() => {
          // 重登成功，重新发送原始请求
          console.log('errCode401 重登成功，重新发送请求')
          return service.request(response.config)
        }).catch(() => {
          // 重登失败，返回错误
          return Promise.reject(response.data)
        })
      } else if (res.code === 500) {
        tip = i18n.t('errCode500')
      } else if (res.code === 1000 || res.code === 1001) {
        tip = i18n.t('errCode1000')
      } else if (res.code === 1100) tip = i18n.t('errCode1100')
      else if (res.code === 1101) tip = i18n.t('errCode1101')
      else if (res.code === 1102) tip = i18n.t('errCode1102')
      else if (res.code === 1258) tip = i18n.t('userLimitTip')
      else if (res.code === 1260) tip = i18n.t('timerangeexceeds')
      else if (res.code === 1002) tip = i18n.t('errCode1002')
      else {
        tip = res.msg
      }
      Message.error(tip)
      return Promise.reject(response.data)
    } else {
      // return response
      return response.data
    }
  },
  error => {
    console.log('err' + error) // for debug
    const execs = /^(timeout of )(\d+)(ms exceeded)$/g.exec(error.message)
    if (execs && execs.length) {
      error.message = execs[2] ? i18n.t('timeout') : error.message
    }
    error.message = error.message.replace(
      /(Network Error)/,
      i18n.t('networkError')
    )
    Message.error(error.message)
    return Promise.reject(error)
  }
)

export default service
