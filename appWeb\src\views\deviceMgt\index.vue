<template>
  <div ref="deviceMgtLayout" class="deviceMgtBox">
    <div class="deviceMgtLayout">
      <div class="groupBox">
        <div class="groupListCon placeholder" />
        <div class="groupListCon fixed">
          <div v-if="group_loading" class="groupLoadingBox">
            <div><i class="el-icon-loading" /></div>
            <div>{{ $t("deviceGroupLoading") }}</div>
          </div>
          <div
            v-for="(item, index) in group_list"
            v-else
            :key="index"
            :class="{ selectedGroup: item.selected_group_flag }"
            class="groupItem pointer"
            @click="changeGroup(item)"
          >
            <div class="groupItemLeft">
              <!-- <el-checkbox
                v-if="item.value !== -1"
                v-model="item.selected_group_flag"
              /> -->
              <span :title="item.label" class="groupName" v-text="item.label" />
            </div>
            <div v-if="account_type == `1`" class="groupItemRight">
              <img
                v-if="item.value !== -1"
                class="operateImg"
                src="@/assets/img/editIcon.png"
                @click.stop="addGroup(item)"
              />
              <img
                v-if="item.value !== -1"
                class="operateImg"
                src="@/assets/img/deleteIcon.png"
                @click.stop="delGroup(item)"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="operateDeviceBox">
        <!-- <div class="operateDeviceBtnBox">
          <el-button
            class="operateDeviceBtn"
            @click="addEditDevice()">{{ $t('addDevice') }}</el-button>
          <el-button
            class="operateDeviceBtn"
            @click="showStrategy()">{{ $t('strategicAttributes') }}</el-button>
          <el-button
            class="operateDeviceBtn bulkDeleteBtn"
            @click="delBulkDevice()">{{ $t('bulkDelete') }}</el-button>
        </div> -->

        <div class="top-area">
          <div class="filterBox">
            <!-- 输入框 -->

            <div class="operationType filterItem">
              <span>{{ $t("device") }}</span>
              <el-input
                v-model="searchText"
                :placeholder="$t('deviceSearchTip')"
                maxlength="100"
                style="width: 240px"
                @keyup.enter.native="search()"
              />
            </div>

            <!-- 搜索按钮 -->
            <el-button
              type="primary"
              class="tipSubmitBtn"
              icon="el-icon-search"
              @click="search()"
            >
              {{ $t("search") }}
            </el-button>

            <!-- 批量设置帧率 -->
            <el-button
              v-if="isSuperAdmin"
              type="primary"
              class="tipSubmitBtn tipSetFpsButton"
              @click="batchSetFps()"
            >
              {{ $t("batchSetFps") }}
            </el-button>
          </div>
        </div>

        <div class="deviceListBox">
          <el-table
            v-loading="list_loading"
            :highlight-current-row="true"
            :data="device_list"
            stripe
            @selection-change="handleSelectionChange"
          >
            <!-- <el-table-column
              type="selection"
              width="55" /> -->
            <el-table-column
              type="index"
              label="#"
              align="center"
              width="60"
              class-name="infoText"
            />
            <el-table-column
              v-for="(l, index) in listTitle"
              :key="index"
              :prop="l.name"
              :label="l.title"
              :width="l.width"
              :column-key="l.name"
              :class-name="
                ['version', 'alias', 'group_name'].includes(l.name)
                  ? 'infoText'
                  : ''
              "
              align="center"
            >
              <template slot-scope="scope">
                <section v-if="scope.column.columnKey === 'net_status'">
                  <span
                    :class="scope.row.net === 0 ? 'offDot' : 'online'"
                    class="dot marRight10"
                  /><span
                    :title="scope.row[scope.column.columnKey]"
                    v-html="handleColumnShow(scope.row[scope.column.columnKey])"
                  />
                </section>
                <section
                  v-else
                  :title="scope.row[scope.column.columnKey]"
                  v-html="handleColumnShow(scope.row[scope.column.columnKey])"
                />
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('operate')"
              min-width="113px"
              align="center"
              v-if="isSuperAdmin"
            >
              <!-- 相关操作按钮 -->
              <template slot-scope="scope">
                <div class="operateButtonLine">
                  <el-button
                    :title="$t('edit')"
                    type="text"
                    class="operateRowBtn"
                    @click="addEditDevice(scope.row)"
                    >{{ $t("edit") }}</el-button
                  >
                  <el-button
                    :title="$t('delete')"
                    type="text"
                    class="operateRowBtn rowDeleteBtn"
                    @click="delDevice(scope.row)"
                    >{{ $t("delete") }}</el-button
                  >
                  <el-button
                    :title="$t('setFps')"
                    type="text"
                    class="operateRowBtn rowSetFpsButton"
                    @click="setFps(scope.row)"
                    >{{ $t("setFps") }}</el-button
                  >
                </div>
              </template>
            </el-table-column>
            <template slot="empty">
              <el-empty
                :image="require('@/assets/img/empty.png')"
                :image-size="65"
              />
            </template>
          </el-table>
          <pagination
            v-show="total > 10"
            :total="total"
            :page.sync="listQuery.page"
            :limit.sync="listQuery.limit"
            class="text-right mar_top_0"
            @pagination="handlePaginationChange"
          />
        </div>
      </div>
    </div>
    <group-add-dialog
      ref="groupAddDialog"
      :title="dialogTitle"
      :number="editGroupNumber"
    />
    <device-add-dialog ref="deviceAddDialog" :title="dialogTitle" />
    <tip-dialog
      ref="tipDialog"
      :title="dialogTitle"
      :tip="dialogTip"
      :request="tipRequest"
      :params="tipParams"
      req-type="http"
      @handleTip="backTip"
    />
    <set-fps-dialog
      ref="setFpsDialog"
      :title="dialogTitle"
      @refresh="refreshList"
    />
  </div>
</template>

<script>
import { setToken } from '@/utils/auth'
import { getDeviceGroupList, getDeviceList, deleteDevice, deleteDeviceGroup } from '@/api/deviceMgt'
import { handleColumnShow } from '@/utils/index'
import Pagination from '@/components/Pagination'
import GroupAddDialog from './groupAddDialog'
import DeviceAddDialog from './deviceAddDialog'
import TipDialog from '@/components/TipDialog'
import autoReloginMixin from '@/mixins/autoRelogin'
import SetFpsDialog from './setFpsDialog.vue'

export default {
  name: 'DeviceMgt',
  components: {
    Pagination,
    GroupAddDialog,
    DeviceAddDialog,
    TipDialog,
    SetFpsDialog
  },
  mixins: [autoReloginMixin],
  data () {
    return {
      os_attr: [],
      group_list: [],
      selected_group: -10,
      group_loading: false,
      device_list: null,
      filter_device_list: [],
      listTitle: [

        {
          title: this.$t('cid'),
          name: 'sn',
          width: '160px'
        },
        {
          title: this.$t('status'),
          name: 'net_status',
          width: '110px'
        },
        {
          title: this.$t('versionNumber'),
          name: 'version',
          width: '130px'
        },
        {
          title: this.$t('deviceNickname'),
          name: 'alias',
          width: '201px'
        },
        {
          title: "FPS",
          name: 'fps',
          width: '80px'
        },
        {
          title: this.$t('group'),
          name: 'group_name'
        }
      ],
      total: 0,
      list_loading: true,
      listQuery: {
        page: 1,
        limit: 20
      },
      dialogTitle: '',
      dialogTip: '',
      tipRequest: '',
      tipParams: {},
      multipleSelection: [],

      editGroupNumber: '0',

      account_type: sessionStorage.account_type,
      searchText: '',

      isSuperAdmin: false
    }
  },
  created () {
    // mixin会自动调用handleAutoRelogin()，这里不需要重复调用

    this.isSuperAdmin = sessionStorage.account_type == '1'
  },
  methods: {
    // 重写mixin的回调方法 - 重登成功后执行
    onReloginSuccess (response) {
      this.getGroup()
    },

    // 重写mixin的回调方法 - websocket准备就绪后执行
    onWebsocketReady () {
      console.log('deviceMgt页面websocket准备就绪')
      // this.getGroup()
    },

    // 获取OS属性表
    getDeviceOs () {
      var post_data = {
        'version': 1,
        'language_type': this.$store.getters.language
      }
      this.$websocket.webSocketSend('cli_os_attr_web_get', post_data)
      this.$bus.$once('cli_os_attr_web_get_rsp', response => {
        if (typeof (response) === 'number') {
          return
        }
        this.os_attr = response.body
      })
    },
    getGroup () {
      this.group_loading = true
      this.list_loading = true
      const gList = [{
        value: -1,
        label: this.$t('allGroup'),
        selected_group_flag: true
      }]
      getDeviceGroupList()
        .then(res => {
          if (res.code === 200) {
            console.log(res.datas)
            res.datas && res.datas.map((item, index) => {
              gList.push({
                value: item.group_id,
                label: item.name
              })
            })
          }
        }).catch(err => {

        }).finally(() => {
          this.group_loading = false
          this.getDeviceList(-1)
        })

      this.group_list = gList
      return
      var post_data = {
        'type': 0
      }
      this.$websocket.webSocketSend('cli_dev_group_list', post_data)
      this.$bus.$once('cli_dev_group_list_rsp', response => {
        if (typeof (response) === 'number') {
          this.group_loading = false
          return
        }
        var list = response.body
        this.group_list = [{
          value: -1,
          label: this.$t('allGroup'),
          selected_group_flag: true
        }]
        list.map((item, index) => {
          this.group_list.push({
            value: item.group_id,
            label: item.alias
          })
        })
        this.group_loading = false
        this.getDeviceList(-1)
      })
    },

    getDeviceList (id, flag) {
      // if (this.selected_group === id && flag !== 'refresh') {
      //   this.list_loading = false
      //   return
      // }
      // 先把之前选中的组样式去掉
      const old_group_list = this.group_list
      if (old_group_list.length > 0) {
        old_group_list.map(item => {
          this.$set(item, 'selected_group_flag', false)
        })
      }
      // 再把当前选中的组加上样式
      this.selected_group = id
      this.group_list.map(m => {
        if (m.value === id) {
          this.$set(m, 'selected_group_flag', true)
        }
      })
      // 更新设备列表
      this.list_loading = true
      const reqData = {
        offset: (this.listQuery.page - 1) * this.listQuery.limit,
        limit: this.listQuery.limit,
        text: this.searchText
      }

      if (id !== -1) {
        reqData.group_id = id
      }

      getDeviceList(reqData)
        .then(res => {
          if (res.code === 200) {
            this.total = res.count
            const list = res.datas.map(i => {
              return {
                sn: i.cid,
                net: i.net,
                net_status: i.net <= 0 ? this.$t('offline') : this.$t('online'),
                version: i.version,
                alias: i.name,
                group_name: i.group_name,
                fps: i.fps
              }
            })
            this.device_list = list
          }
        }).catch(err => {

        }).finally(() => {
          this.list_loading = false
        })
      return
      let post_data = {}
      if (id === -1) {
        post_data = {
          'time': 0,
          'type': 1,
          'page': this.listQuery.page,
          'num': this.listQuery.limit
        }
        this.$websocket.webSocketSend('cli_dev_list_ungroup', post_data)
        this.$bus.$once('cli_dev_list_ungroup_rsp', response => {
          if (typeof (response) === 'number') {
            this.list_loading = false
            return
          }
          this.handelDeviceList(id, response.body)
        })
      } else {
        post_data = {
          'time': 0,
          'type': 1,
          'group_id': id,
          'page': this.listQuery.page,
          'num': this.listQuery.limit
        }
        this.$websocket.webSocketSend('cli_dev_list', post_data)
        this.$bus.$once('cli_dev_list_rsp', response => {
          if (typeof (response) === 'number') {
            this.list_loading = false
            return
          }
          this.handelDeviceList(id, response.body)
        })
      }
    },
    handelDeviceList (id, res) {
      let list = []
      if (id === -1) {
        list = res.list
        this.total = res.total
      } else {
        const all_group_device = res.list
        all_group_device.map(m => {
          if (m.group_id === id) {
            list = m.sn_list
            list.map(t => {
              t.group_id = id
            })
            this.total = m.total
          }
        })
      }

      this.device_list = []
      list.map((item, index) => {
        item.net = 0
        this.group_list.map(obj => {
          if (item.group_id === obj.value) {
            item.group_name = obj.label
          }
        })
      })
      if (list.length > 0) {
        this.getDeviceNet(list)
      } else {
        this.list_loading = false
        this.device_list = []
      }
    },
    handlePaginationChange (val) {
      console.log(val)
      this.listQuery = {
        page: val.page,
        limit: val.limit
      }
      this.getDeviceList(this.selected_group)

      const deviceMgtLayout = this.$refs['deviceMgtLayout']
      deviceMgtLayout.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },
    getDeviceNet (list) {
      const temp_obj = {}
      list.map(item => {
        temp_obj[item.sn] = [{ 'id': 201 }, { 'id': 207 }]
      })
      var post_data = {
        'limit': list.length,
        'asc': true,
        'equal': true,
        'req_map': temp_obj
      }
      this.$websocket.webSocketSend('pub_dp_get_muti', post_data)
      this.$bus.$once('pub_dp_get_muti_rsp', response => {
        if (typeof (response) === 'number') {
          this.list_loading = false
          this.device_list = list
          return
        }
        var res = response.body
        list.map(i => {
          if (res[i.sn][201].length > 0) {
            i.net = JSON.parse(res[i.sn][201][0]['value']).net
          } else {
            i.net = 0
          }
          if (i.net <= 0) {
            i.net_status = this.$t('offline')
          } else {
            i.net_status = this.$t('online')
          }
          if (res[i.sn][207].length > 0) {
            i.version = JSON.parse(res[i.sn][207][0]['value']).str_val
          } else {
            i.version = ''
          }
          if (this.os_attr.length > 0) {
            this.os_attr.map(m => {
              if (i.os === parseInt(m.os)) {
                Object.assign(i, m)
              }
            })
          }
        })
        this.device_list = list
        console.log(this.device_list)

        this.list_loading = false
      })
    },
    handleColumnShow,
    addGroup (val) {
      if (val) {
        this.dialogTitle = this.$t('editGroup')
        this.editGroupNumber = val.value
      } else {
        this.dialogTitle = this.$t('addGroup')
      }
      this.$refs.groupAddDialog.show(val)
    },
    delGroup (val) {
      this.dialogTitle = this.$t('delGroup')
      this.dialogTip = this.$t('sureToDelete')
      this.tipRequest = 'cli_dev_group_del'
      this.tipParams = {
        'group_id': val.value
      }
      this.$refs.tipDialog.show('delGroup')
        .then(res => {
          if (res) {
            const reqData = {
              group_id: val.value
            }
            deleteDeviceGroup(reqData)
              .then(res => {
                if (res.code === 200) {
                  this.$refs.tipDialog.hideDialog()
                  this.getGroup()
                  this.$message.success(this.$t('deleteSuccess'))
                }
              })
          }
        })
    },
    backTip (val) {
      this.$message.success(this.$t('deleteSuccess'))
      if (val === 'delGroup') {
        this.getGroup()
      } else if (val === 'delDevice' || val === 'bulkDelDevice') {
        this.getDeviceList(this.selected_group, 'refresh')
      }
    },
    // 添加设备，编辑设备
    addEditDevice (val) {
      console.log(val)
      if (val) {
        this.dialogTitle = this.$t('editDevice')
      } else {
        this.dialogTitle = this.$t('addDevice')
      }
      this.$refs.deviceAddDialog.show(this.selected_group, val)
    },
    // 单个删除设备
    delDevice (val) {
      this.dialogTitle = this.$t('deleteDevice')
      this.dialogTip = this.$t('sureToDelete')
      this.tipRequest = 'cli_dev_del'
      this.tipParams = {
        'sn': val.sn
      }
      this.$refs.tipDialog.show('delDevice')
        .then(res => {
          if (res) {
            const reqData = {
              cid: val.sn
            }
            deleteDevice(reqData)
              .then(res => {
                if (res.code === 200) {
                  this.$refs.tipDialog.hideDialog()
                  this.getDeviceList(this.selected_group)
                  this.$message.success(this.$t('deleteSuccess'))
                }
              })
          }
        })
    },
    // 多选
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    delBulkDevice () {
      console.log(this.multipleSelection)
      if (this.multipleSelection.length <= 0) {
        this.$message.error(this.$t('bulkDelDeviceTip'))
        return
      }
      const sn_arr = []
      this.multipleSelection.map(item => {
        sn_arr.push(item.sn)
      })
      this.dialogTitle = this.$t('bulkDelete')
      this.dialogTip = this.$t('sureToDelete')
      this.tipRequest = 'cli_dev_del_muti'
      this.tipParams = {
        'sn': sn_arr
      }
      this.$refs.tipDialog.show('bulkDelDevice')
    },
    showStrategy (val) {
      console.log(val)
      console.log(this.selected_group)
    },
    search () {
      this.getDeviceList(this.selected_group)
    },
    batchSetFps () {
      this.$refs.setFpsDialog.show(this.selected_group, '' /* sn */)
      this.dialogTitle = this.$t('batchSetFps')
    },
    setFps (row) {
      this.$refs.setFpsDialog.show(row.group_id, row.sn, row.fps)
      this.dialogTitle = this.$t('setFps')
    },
    changeGroup (val) {
      if (val.value !== this.selected_group) {
        this.selected_group = val.value
        this.searchText = ''
        this.getDeviceList(this.selected_group)
      }
    },
    refreshList () {
      this.getDeviceList(this.selected_group)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
div,
text {
  color: var(--color-neutral-100);
  font-size: var(--font-size-normal);
}
div {
  &::-webkit-scrollbar-track {
    background-color: var(--color-neutral-600); /* 设置轨道颜色 */
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--color-neutral-500) !important; /* 设置滑块颜色 */
    border-radius: 10px; /* 圆角滑块 */
  }
}
.deviceMgtBox {
  height: calc(100vh - 74px);
  background: var(--color-neutral-700);
  overflow-y: scroll;
}
.deviceMgtLayout {
  display: flex;
  justify-content: center;
  max-width: 1260px;
  box-sizing: border-box;
  margin: auto;
  padding: 24px 20px 24px 0;
}
.groupBox {
  width: 260px;
  max-height: calc(100vh - 74px);
}
.deviceGroupBox {
  padding: 30px 9.7% 25px 15.7%;
}
.groupListTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid #b4b4b4;
  font-size: 18px;
  color: #1e2224;
  box-sizing: border-box;
}
.selectGroupBox {
  font-size: 18px;
  color: #a4aeb9;
}
.loadImg {
  vertical-align: bottom;
}
.groupListCon {
  font-size: 16px;
  color: #2c3538;
  height: calc(100vh - 74px - 24px - 38px);
  overflow: auto;
  border-radius: 6px;
}
.top-area {
  display: flex;
  flex-wrap: wrap;
  .tipSetFpsButton {
    background-color: var(--color-accent-400);
    &:hover {
      opacity: 0.9;
    }
  }
}
.groupListCon.placeholder {
  opacity: 0;
  position: relative;
  z-index: -1;
  // max-height: calc(100vh - 74px);
  display: none;
}
.groupListCon.fixed {
  width: 260px;
  position: fixed;
  top: 94px;
  background-color: var(--color-neutral-600);
}
.groupLoadingBox {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #f4f9fd;
  i {
    font-size: 50px;
    color: #666b6e;
    margin-bottom: 20px;
  }
}
.groupItem {
  height: 66px;
  line-height: 66px;
  display: flex;
  justify-content: space-between;
  padding: 0 10px 0 20px;
  border-bottom: 1px solid var(--color-neutral-500);
  &:hover {
    background: var(--color-neutral-hover);
  }
  .groupItemLeft {
    width: calc(100% - 60px);
    display: flex;
    align-items: center;
    .groupName {
      margin-left: 10px;
      line-height: normal;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.selectedGroup {
  color: #2daada;
  background: var(--color-primary);
  &:hover {
    background: var(--color-primary);
  }
  .groupItemLeft {
    font-weight: bolder;
  }
  .groupName {
    color: var(--color-white);
  }
}
.operateImg {
  width: 26px;
  height: 26px;
}
.operateDeviceBox {
  width: calc(100% - 260px);
  padding: 0 0 0 30px;
  overflow: auto;
  .operateDeviceBtnBox {
    margin-bottom: 30px;
  }
  .operateDeviceBtn {
    border: 1px solid #2daada;
    border-radius: 6px;
    background: #eceff5;
    font-size: 16px;
    font-weight: bold;
    color: #2daada;
  }
  .bulkDeleteBtn {
    border: 1px solid #8496a3;
    color: #8496a3;
  }
}
.deviceListBox {
  border-radius: 6px;
  background: var(--color-neutral-600);
}
.operateRowBtn {
  padding: 0;
  color: var(--color-accent-400);
  border-radius: 0;
}
.rowStrategyBtn {
  color: #01b974;
  border-bottom: 1px solid #01b974;
}
.rowDeleteBtn {
  color: var(--color-negative-400);
}
.rowSetFpsButton {
  color: #86c8ff;
}
.marRight10 {
  margin-right: 10px;
}
.el-table {
  color: #101214;
  min-height: calc(100vh - 450px);
}
.online {
  background-color: var(--color-primary);
}
/deep/ .el-table thead {
  font-size: 16px;
  color: #101214;
}
/deep/
  .el-table--striped
  .el-table__body
  tr.el-table__row--striped
  td.el-table__cell {
  background: #eff3f7;
}

.groupItemRight {
  display: flex;
  align-items: center;
}
</style>

