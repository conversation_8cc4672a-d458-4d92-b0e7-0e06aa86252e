import Vue from 'vue'
import { Message } from 'element-ui'
import store from '@/store'
import i18n from '@/lang'
import router from '@/router'
import tabManager from '@/utils/tabManager'
import reloginManager from '@/utils/reloginManager'
import { removeToken, removeRole } from '@/utils/auth'

var ws_server = sessionStorage.wsServer
var connecte_type = ''
var socket = {}
var req_id = 1
var heartbeat = null
var isManualClose = false // 标记是否为主动关闭
var net_type = getNetworkType().type
var net_name = getNetworkType().name

var isLogedin = false

function initWebSocket (type, wsServer) {
  console.log('init')
  console.log(this)
  connecte_type = type
  isManualClose = false // 重置主动关闭标志
  if (wsServer) {
    ws_server = wsServer
  }
  socket = new WebSocket(ws_server)
  socket.onopen = webSocketOnOpen
  socket.onerror = webSocketOnError
  socket.onmessage = webSocketOnMessage
  socket.onclose = webSocketOnClose
  console.log('socket: ', socket)
  if (this) {
    this.socket = socket
  }
}
function webSocketOnOpen (e) {
  console.log('connected')
  Vue.prototype.$bus.$emit('webSocketConnecteType', connecte_type)
  if (
    connecte_type === 'closeReconnection' ||
    connecte_type === 'refreshReconnection'
  ) {
    const post_data = {
      language_type: 0,
      account: sessionStorage.username,
      passwd: '',
      os: 1,
      net: net_type,
      name: net_name,
      bundle_id: '**********',
      device_token: 'token' + code_temp,
      sessid: store.getters.token,
      vid: '0001',
      vkey: 'DcWP670PNfCtPIETQk03lEzbt6qRDRDy'
    }
    webSocketSend('cli_miru_login', post_data)
    connecte_type = ''
  }
  if (!heartbeat) {
    heartbeat = setInterval(function () {
      webSocketSend('pub_heartbeat', {})
    }, 60000)
  }
}
function webSocketOnError (e) {
  console.log('WebSocket连接error', e)
  // ElementUI.Notification({
  //     title: '',
  //     message: "WebSocket连接发生错误" + e,
  //     type: 'error',
  //     duration: 0,
  // });
}
function webSocketOnMessage (e) {
  const data = JSON.parse(e.data)
  handleReceivedMessage(data)
}
// 关闭websocket
function webSocketOnClose () {
  console.log('连接已关闭...')
  console.log('是否有token：' + store.getters.token)
  console.log('connecte_type: ', connecte_type)
  console.log('isManualClose: ', isManualClose)

  if (heartbeat) {
    clearInterval(heartbeat)
    heartbeat = null
  }

  // 如果是主动关闭，则不重连
  if (isManualClose) {
    console.log('主动关闭WebSocket，不进行重连')
    isManualClose = false // 重置标志
    return
  }

  // 如果有token时，websocket断开了就重连
  if (store.getters.token) {
    handleWebSocketReconnection()
  }
}

// 处理 WebSocket 重连逻辑
function handleWebSocketReconnection () {
  console.log('WebSocket 断开，检查是否需要重连')

  // 检查当前 tab 数量
  const tabCount = tabManager.getTabCount()
  console.log('当前 tab 数量:', tabCount)

  if (tabCount > 1) {
    // 如果有多个 tab，说明可能是其他 tab 登录导致的断开
    // 直接返回登录页，不进行重登，也不清除 session
    console.log('检测到多个 tab，可能是其他 tab 登录导致的断开，返回登录页')

    // 执行轻量级登出：只清除当前 tab 的状态，不清除共享的 sessid
    handleMultiTabLogout()

    return
  }

  // 只有一个 tab 时，执行正常的重连逻辑
  console.log('只有一个 tab，执行正常重连')
  initWebSocket('closeReconnection')
}

// 处理多 tab 情况下的登出：只清除当前 tab 状态，保留共享 sessid
function handleMultiTabLogout () {
  console.log('执行多 tab 登出：保留共享 sessid，只清除当前 tab 状态')

  // 主动断开 WebSocket 连接
  try {
    closeWebsocket()
    console.log('WebSocket connection closed during multi-tab logout')
  } catch (error) {
    console.warn('Error closing WebSocket during multi-tab logout:', error)
  }

  // 清除 store 状态
  store.commit('SET_TOKEN', '')
  store.commit('SET_ROLES', [])
  store.commit('SET_ROLE', '')

  // 清除 sessionStorage（当前 tab 的状态）
  sessionStorage.clear()

  // 清除 auth token
  removeToken()
  removeRole()

  // 注意：不调用 tabManager.clearSessid()，保留共享的 sessid
  console.log('当前 tab 状态已清除，共享 sessid 保留')

  // 跳转到登录页
  if (router.currentRoute.path !== '/login') {
    router.replace('/login')
  }
}
function closeWebsocket () {
  console.log('closeWebsocket  socket: ', socket)

  // 设置主动关闭标志，防止自动重连
  isManualClose = true

  if (socket && socket.url) {
    socket.close() // 关闭 websocket
    // 不需要重新设置 onclose，让原有的 webSocketOnClose 处理
  }

  if (heartbeat) {
    clearInterval(heartbeat)
    heartbeat = null
  }
}
function webSocketSend (id, bodyData, callee) {
  var send_data = ''
  if (callee) {
    send_data = JSON.stringify({
      headers: {
        id: id,
        callee: callee,
        req_id: getReqId(),
        time: Date.parse(new Date()) / 1000
      },
      body: bodyData
    })
  } else {
    send_data = JSON.stringify({
      headers: {
        id: id,
        req_id: getReqId(),
        time: Date.parse(new Date()) / 1000
      },
      body: bodyData
    })
  }
  socket.send(send_data)
}

// #####################################
var code_temp = generatedCode()
function getReqId () {
  var id = 'web_' + code_temp + '_' + req_id
  req_id++
  return id
}
function generatedCode () {
  if (localStorage && localStorage.getItem('code_temp')) {
    return localStorage.getItem('code_temp')
  }

  const random = [
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z'
  ]
  let code = ''
  for (let i = 0; i < 6; i++) {
    var index = Math.floor(Math.random() * 36)
    code += random[index]
  }

  localStorage.setItem('code_temp', code)

  return code
}
function handleReceivedMessage (data) {
  // 若登录成功，改变登录状态
  if (data.headers.id === 'cli_miru_login_rsp') {
    isLogedin = data.ret === 0
    // 如果登录成功，重置重登状态
    if (data.ret === 0) {
      reloginManager.reset()
    }
  }

  // DP消息 没有返回ret  只有回复的消息rsp才返回ret
  if (data.ret === undefined || data.ret === 0) {
    Vue.prototype.$bus.$emit(data.headers.id, data)
  } else {
    var tip = ''
    console.log('recv', data.ret)
    if (data.ret === 120) {
      tip = i18n.t('errCode120')
    } else if (data.ret === 121) {
      tip = i18n.t('errCode121')
    } else if (data.ret === 122) {
      tip = i18n.t('errCode122')
      setTimeout(() => {
        store.dispatch('FedLogOut')
        location.reload()
      }, 3 * 1000)
    } else if (data.ret === 123) {
      tip = i18n.t('errCode123')
    } else if (data.ret === 124) {
      tip = i18n.t('errCode124')
    } else if (data.ret === 125) {
      tip = i18n.t('errCode125')
    } else if (data.ret === 126) {
      tip = i18n.t('errCode126')
    } else if (data.ret === 127) {
      tip = i18n.t('errCode127')
    } else if (data.ret === 128) {
      tip = i18n.t('errCode128')
    } else if (data.ret === 129) {
      // 当前未登录，需要登录才能进行此操作
      // 尝试重登，重登失败的话就继续提示errCode129的多语言，然后再ForceLogout
      handleErrCode129()
      return // 不执行后续的错误提示逻辑，由 handleErrCode129 处理
    } else if (data.ret === 161) {
      tip = i18n.t('errCode161')
    } else if (data.ret === 165) {
      tip = i18n.t('errCode165')
      // 账号已在其他设备登录
      // 如果当前不在登录页，则强制跳转到登录页并清除历史记录
      if (router.currentRoute.path !== '/login') {
        forceLogout()
      }
    } else if (data.ret === 182) {
      tip = i18n.t('errCode182')
    } else if (data.ret === 290) {
      tip = i18n.t('errCode290')
    } else if (data.ret === 291) {
      tip = i18n.t('errCode291')
    } else if (data.ret === 292) {
      tip = i18n.t('errCode292')
    } else if (data.ret === 1002) {
      tip = i18n.t('errCode1002')
    } else if (data.ret === 1004) {
      tip = i18n.t('errCode1004')
    } else if (data.ret === 2008) {
      tip = i18n.t('errCode2008')
    } else if (data.ret === 2011) {
      tip = i18n.t('errCode2011')
    } else if (data.ret === 2030) {
      tip = i18n.t('errCode2030')
    } else if (data.ret === 2031) {
      tip = i18n.t('errCode2031')
    } else if (data.ret === 2032) {
      tip = i18n.t('errCode2032')
    } else if (data.ret === 1260) {
      tip = i18n.t('timerangeexceeds')
    } else {
      tip = data.msg
    }
    Message.error(tip)
    Vue.prototype.$bus.$emit(data.headers.id, data.ret)
  }
}

// 处理 errCode129 的重登逻辑
function handleErrCode129 () {
  console.log('收到 errCode129，尝试重登')

  reloginManager.startRelogin(() => {
    return new Promise((resolve, reject) => {
      // 获取缓存的  account 和 password
      // const cachedSessid = tabManager.getSessid()
      const cachedAccount = tabManager.getAccount()
      const cachedPasswd = tabManager.getPassword()

      if (!cachedAccount) {
        console.log('没有缓存的  account，直接显示错误提示并强制登出')
        Message.error(i18n.t('errCode129'))
        setTimeout(() => {
          forceLogout()
        }, 2000)
        reject(new Error('errCode129'))
        return
      }

      console.log('发现缓存的  account，尝试重登')

      // 构造重登数据，参考登录页的重登逻辑
      const post_data = {
        language_type: 0,
        account: cachedAccount,
        passwd: cachedPasswd,
        os: 1,
        net: net_type,
        name: net_name,
        bundle_id: '**********',
        device_token: 'token' + code_temp,
        vid: '0001',
        vkey: 'DcWP670PNfCtPIETQk03lEzbt6qRDRDy'
      }

      // 发送重登请求
      webSocketSend('cli_miru_login', post_data)

      // 监听重登响应
      Vue.prototype.$bus.$once('cli_miru_login_rsp', response => {
        console.log('errCode129 重登响应:', response)

        if (typeof response === 'number') {
          // 重登失败，显示 errCode129 的多语言提示，然后强制登出
          console.log('errCode129 重登失败，显示错误提示并强制登出')
          setTimeout(() => {
            forceLogout()
          }, 2000)
          reject(new Error('errCode' + response))
          return
        }

        // 重登成功，更新登录状态
        console.log('errCode129 重登成功')
        isLogedin = true

        // 更新 store 和 sessionStorage 中的数据
        store.commit('SET_TOKEN', response.body.sessid)
        sessionStorage.token = response.body.sessid

        if (response.body.account_type !== undefined) {
          sessionStorage.account_type = response.body.account_type
        }
        if (response.body.account) {
          sessionStorage.username = response.body.account
        }
        if (response.body.alias) {
          sessionStorage.account_alias = response.body.alias
        }
        if (response.body.srs_http) {
          sessionStorage.videoUrl = JSON.stringify(response.body.srs_http)
        }

        // 更新 localStorage 中的 sessid 和 account
        tabManager.setSessid(response.body.sessid, response.body.account)

        console.log('errCode129 重登成功，登录状态已更新')
        resolve()
      })
    })
  }).catch((error) => {
    console.log('errCode129 重登失败:', error.message)
    Message.error(i18n.t(error.message))
    setTimeout(() => {
      forceLogout()
    }, 2000)
  })
}

function forceLogout () {
  // 重置重登状态
  reloginManager.reset()
  // 清除用户数据
  store.dispatch('FedLogOut')
  // 使用 replace 方式跳转到登录页，清除历史记录
  router.replace('/login')
  // 清除浏览器历史记录，防止用户点击返回键
  if (window.history && window.history.pushState) {
    window.history.replaceState(null, null, '/login')
    // 监听浏览器返回事件，强制保持在登录页
    const preventBack = () => {
      window.history.replaceState(null, null, '/login')
    }
    window.addEventListener('popstate', preventBack)
    // 设置一个定时器，在一段时间后移除监听器（避免影响正常的页面导航）
    setTimeout(() => {
      window.removeEventListener('popstate', preventBack)
    }, 2000)
  }
}

function getNetworkType () {
  var ua = navigator.userAgent
  var networkStr = ua.match(/NetType\/\w+/)
    ? ua.match(/NetType\/\w+/)[0]
    : 'NetType/other'
  networkStr = networkStr.toLowerCase().replace('nettype/', '')
  var networkType = 1
  switch (networkStr) {
    case 'wifi':
      networkType = 1
      name = 'wifi'
      break
    case '5g':
      networkType = 5
      name = '5g'
      break
    case '4g':
      networkType = 4
      name = '4g'
      break
    case '3g':
      networkType = 3
      name = '3g'
      break
    case '3gnet':
      networkType = 3
      name = '3g'
      break
    case '2g':
      networkType = 2
      name = '2g'
      break
    default:
      networkType = 10
      name = 'wired'
  }
  return {
    type: networkType,
    name: name
  }
}

class MiruWebsocket {
  constructor() {
    this.initWebSocket = initWebSocket
    this.closeWebsocket = closeWebsocket
    this.webSocketSend = webSocketSend
    this.generatedCode = generatedCode
    this.isLogIn = () => isLogedin
    this.isLogedin = false
    this.handleReceivedMessage = handleReceivedMessage
    this.close = () => socket.close()
  }
}

export default new MiruWebsocket()
