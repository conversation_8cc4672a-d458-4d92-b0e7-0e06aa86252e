<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    append-to-body
    @close="hideDialog"
    class="tipDialog"
    :before-close="handleCancelIcon"
  >
    <section>
      <div class="content">
        <div>
          <p class="tip">{{ tip }}</p>
        </div>
      </div>
    </section>
    <span slot="footer" class="dialog-footer">
      <el-button type="info" class="tipCancelBtn" @click="hideDialog">{{
        $t("cancel")
      }}</el-button>
      <el-button
        :loading="loading"
        type="primary"
        class="tipSubmitBtn"
        @click="submit"
        >{{ $t("sure") }}</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { removeToken, removeRole } from '@/utils/auth'
export default {
  name: 'TipDialog',
  components: {

  },
  props: {
    request: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default () {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    tip: { // 提示语句
      type: String,
      default: ''
    },
    reqType: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      dialogVisible: false,
      name: '',
      loading: false,

      isEnd: null
    }
  },
  methods: {
    async show (val) {
      this.dialogVisible = true
      this.name = val
      console.log(this.name)
      return new Promise(resolve => {
        this.isEnd = new Proxy({
          status: null
        }, {
          set (target, key, value) {
            if (key === 'status' && value === true) {
              resolve(true)
            }else if (key === 'status' && value === false) {
              resolve(false)
            }
            return true
          }
        })
      })
    },
    hideDialog () {
      this.dialogVisible = false
      this.isEnd.status = false
      // setTimeout(() => {
      //   this.isEnd = null
      // }, 25);
      this.loading = false
    },
    submit () {
      this.loading = true
      this.isEnd.status = true
      // 登出就不用等回复了
      if (this.name === 'logout') {
        this.loading = false
        this.$store.dispatch('FedLogOut')
        location.reload()
      } else if (this.reqType === 'http') {

      }
      else {
        this.$websocket.webSocketSend(this.request, this.params)
        let rsp = this.request + '_rsp'
        this.$bus.$once(rsp, response => {
          this.$emit('handleTip', this.name, response)
          this.hideDialog()
        })
        this.loading = false
      }
    },
    handleCancelIcon(done) {
      this.$emit('handleCancel')
      done()
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/deep/ .el-dialog {
  width: 346px;
}
.content {
  display: flex;
  justify-content: center;
  align-items: center;
  word-break: normal;
  .tip {
    margin: 0;
    margin-bottom: 24px;
    color: var(--color-neutral-100);
  }
}
</style>
