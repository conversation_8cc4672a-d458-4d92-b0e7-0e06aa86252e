# errCode401 重登机制导致的对象冻结问题修复

## 问题确认

用户反馈的问题确实是由我们新增的 errCode401 重登机制导致的！

### 问题流程

1. **用户在 video 页面点击刷新** → 触发 HTTP 请求
2. **服务器返回 401 错误** → 触发 `handleErrCode401()` 重登逻辑
3. **重登成功后** → 调用 `service.request(response.config)` 重新发送原始请求
4. **关键问题**：`response.config` 是 axios 的响应配置对象，**被 axios 内部冻结了**
5. **请求拦截器执行** → 试图修改 `response.config.data.time` 和 `response.config.data.auth_token`
6. **属性赋值失败** → 抛出 `can't assign to property "time"` 错误

### 错误信息解析

```
can't assign to property "time" on "{"time":1761360455,"auth_token":"cn0001c48AikL0JdY9q8LpvtPIg0YvcWODn0"}": not an object
```

- 错误发生在 `appWeb/src/utils/request.js` 第 156 行
- `response.config.data` 对象被 axios 冻结，无法修改属性
- "not an object" 实际意思是"不是一个可修改的对象"

## 修复方案

### 1. 请求拦截器防护（已修复）

**文件**: `appWeb/src/utils/request.js` 第 132-176 行

```javascript
service.interceptors.request.use(
  config => {
    const reqConfig = { ...config }
    
    // 确保 data 是一个可修改的对象，避免冻结对象导致的属性赋值错误
    if (reqConfig.data === undefined) {
      reqConfig.data = {}
    } else {
      // 创建一个新的对象，避免修改原始的 config.data（可能被冻结）
      try {
        reqConfig.data = { ...reqConfig.data }
      } catch (e) {
        // 如果浅拷贝失败，尝试深拷贝
        try {
          reqConfig.data = JSON.parse(JSON.stringify(reqConfig.data))
        } catch (deepCopyError) {
          console.warn('request拦截器: data 对象拷贝失败，创建新对象:', deepCopyError)
          reqConfig.data = {}
        }
      }
    }
    
    // 现在安全地添加属性
    try {
      reqConfig.data.time = Math.round(new Date().valueOf() / 1000)
      reqConfig.data.auth_token = store.getters.token
    } catch (assignError) {
      console.error('request拦截器: 属性赋值失败:', assignError)
      // 如果还是失败，创建一个全新的对象
      const originalData = reqConfig.data
      reqConfig.data = {
        ...originalData,
        time: Math.round(new Date().valueOf() / 1000),
        auth_token: store.getters.token
      }
    }
    
    return reqConfig
  }
)
```

### 2. 重新发送请求时的配置处理（已修复）

**文件**: `appWeb/src/utils/request.js` 第 184-219 行

```javascript
if (res.code === 401) {
  return handleErrCode401().then(() => {
    console.log('errCode401 重登成功，重新发送请求')
    
    // 创建一个新的请求配置，避免使用可能被冻结的 response.config
    const originalConfig = response.config
    const newConfig = {
      method: originalConfig.method,
      url: originalConfig.url,
      baseURL: originalConfig.baseURL,
      headers: { ...originalConfig.headers },
      params: originalConfig.params ? { ...originalConfig.params } : undefined,
      data: originalConfig.data ? { ...originalConfig.data } : undefined,
      timeout: originalConfig.timeout,
      responseType: originalConfig.responseType,
      // 复制其他可能需要的配置
      ...originalConfig
    }
    
    // 确保 data 是一个新对象，避免冻结问题
    if (newConfig.data && typeof newConfig.data === 'object') {
      try {
        newConfig.data = JSON.parse(JSON.stringify(newConfig.data))
      } catch (e) {
        console.warn('errCode401 重新请求: data 序列化失败，使用原始数据')
      }
    }
    
    return service.request(newConfig)
  })
}
```

## 修复的关键点

### 1. **双重防护机制**

- **请求拦截器层面**：确保任何传入的 config.data 都是可修改的
- **重新发送请求层面**：创建全新的配置对象，避免使用冻结的 response.config

### 2. **多层错误处理**

- **浅拷贝** → **深拷贝** → **新对象创建** → **对象展开**
- 每一层都有异常捕获，确保最终能成功添加属性

### 3. **保持功能完整性**

- 保留原始请求的所有配置信息
- 确保重新发送的请求与原始请求完全一致
- 不影响正常的请求流程

## 其他潜在问题检查

### 1. **WebSocket 重登不受影响**

errCode401 重登使用的是 WebSocket 发送重登请求，不会触发 HTTP 请求拦截器，所以不会有对象冻结问题。

### 2. **其他 HTTP 重试机制**

检查了整个代码库，没有发现其他直接使用 `response.config` 重新发送请求的地方。

### 3. **第三方库兼容性**

修复方案兼容各种可能的对象状态：
- 普通对象
- 冻结对象 (Object.freeze)
- 只读属性对象
- Proxy 对象
- Vue 响应式对象

## 测试建议

### 1. **基础功能测试**

- video 页面点击刷新按钮
- 确认不再出现 JavaScript 错误
- 确认重登功能正常工作

### 2. **边界情况测试**

- 网络不稳定环境下的重登
- 多次快速刷新
- 不同类型的 HTTP 请求重登

### 3. **兼容性测试**

- 不同浏览器环境
- 开发/生产环境
- 不同的请求数据类型

## 总结

这个问题的根本原因确实是我们新增的 errCode401 重登机制。axios 内部会冻结 `response.config` 对象，当我们尝试重新发送请求时，请求拦截器试图修改冻结的对象导致错误。

通过双重防护机制，我们彻底解决了这个问题：
1. **请求拦截器**：处理任何可能的冻结对象
2. **重新发送请求**：创建全新的配置对象

现在用户在 video 页面点击刷新时，不会再看到令人困惑的 JavaScript 错误，重登功能也能正常工作。
