# JavaScript 错误直接显示给用户的问题修复

## 问题描述

用户在 video 页面点击刷新按钮时，有时会弹出 JavaScript 错误信息：

```
can't assign to property "time" on "{"time":1761360455,"auth_token":"cn0001c48AikL0JdY9q8LpvtPIg0YvcWODn0"}": not an object
```

这是一个 JavaScript 运行时错误直接显示给了用户，非常不友好。

## 问题根本原因

### 1. 错误信息直接传递给用户界面

在 `request.js` 和 `websocket.js` 中，有以下问题代码：

**request.js 第 103 行**：
```javascript
Message.error(i18n.t(error.message))
```

**websocket.js 第 431 行**：
```javascript
Message.error(i18n.t(error.message))
```

这些代码直接将 `error.message`（JavaScript 原始错误信息）传递给 `i18n.t()` 进行翻译。当翻译系统找不到对应的翻译键时，会直接返回原始的 JavaScript 错误信息，导致用户看到技术性的错误消息。

### 2. WebSocket 发送数据时的潜在问题

错误信息显示的是关于 `time` 属性赋值的问题，可能的原因：
- 传递给 `webSocketSend` 的 `bodyData` 对象被冻结（frozen）
- 对象包含不可写的属性
- 对象序列化/反序列化过程中出现问题

## 解决方案

### 1. 修复错误信息显示问题

**修改前**：
```javascript
// request.js
Message.error(i18n.t(error.message))

// websocket.js  
Message.error(i18n.t(error.message))
```

**修改后**：
```javascript
// request.js
Message.error(i18n.t('errCode401'))

// websocket.js
Message.error(i18n.t('errCode129'))
```

**原理**：
- 不再直接使用 `error.message` 作为翻译键
- 使用预定义的错误代码，确保有对应的翻译
- JavaScript 错误信息仍然会在控制台中记录，便于开发调试
- 用户只看到友好的多语言错误提示

### 2. 增强 WebSocket 发送函数的健壮性

**修改前**：
```javascript
function webSocketSend (id, bodyData, callee) {
  var send_data = ''
  if (callee) {
    send_data = JSON.stringify({
      headers: {
        id: id,
        callee: callee,
        req_id: getReqId(),
        time: Date.parse(new Date()) / 1000
      },
      body: bodyData
    })
  } else {
    send_data = JSON.stringify({
      headers: {
        id: id,
        req_id: getReqId(),
        time: Date.parse(new Date()) / 1000
      },
      body: bodyData
    })
  }
  socket.send(send_data)
}
```

**修改后**：
```javascript
function webSocketSend (id, bodyData, callee) {
  try {
    var send_data = ''
    // 确保 bodyData 是一个可序列化的对象，避免冻结对象导致的问题
    var safeBodyData = bodyData
    if (bodyData && typeof bodyData === 'object') {
      try {
        // 创建一个新的对象副本，避免修改原始对象
        safeBodyData = JSON.parse(JSON.stringify(bodyData))
      } catch (e) {
        console.warn('WebSocket send: bodyData 序列化失败，使用原始数据:', e)
        safeBodyData = bodyData
      }
    }
    
    if (callee) {
      send_data = JSON.stringify({
        headers: {
          id: id,
          callee: callee,
          req_id: getReqId(),
          time: Date.parse(new Date()) / 1000
        },
        body: safeBodyData
      })
    } else {
      send_data = JSON.stringify({
        headers: {
          id: id,
          req_id: getReqId(),
          time: Date.parse(new Date()) / 1000
        },
        body: safeBodyData
      })
    }
    socket.send(send_data)
  } catch (error) {
    console.error('WebSocket send 失败:', error)
    // 不要直接显示 JavaScript 错误给用户
    console.error('发送数据:', { id, bodyData, callee })
    // 可以选择性地显示一个用户友好的错误消息
    // Message.error('网络通信异常，请稍后重试')
  }
}
```

**改进点**：
1. **异常捕获**：整个函数用 try-catch 包装
2. **数据安全处理**：对 `bodyData` 进行深拷贝，避免修改原始对象
3. **序列化保护**：如果深拷贝失败，使用原始数据
4. **错误日志**：详细记录错误信息和相关数据，便于调试
5. **用户友好**：不向用户显示技术性错误信息

## 修改的文件

1. **`appWeb/src/utils/request.js`**
   - 第 103 行：修改错误信息显示逻辑

2. **`appWeb/src/utils/websocket.js`**
   - 第 431 行：修改错误信息显示逻辑
   - 第 178-201 行：增强 `webSocketSend` 函数的健壮性

## 技术原理

### 为什么会出现 "time" 属性赋值错误？

1. **对象冻结**：如果 `bodyData` 对象被 `Object.freeze()` 冻结，任何修改都会失败
2. **属性描述符**：对象的属性可能被设置为不可写（writable: false）
3. **Proxy 对象**：某些框架可能使用 Proxy 包装对象，限制属性访问
4. **序列化问题**：JSON 序列化/反序列化过程中可能出现问题

### 深拷贝的作用

```javascript
safeBodyData = JSON.parse(JSON.stringify(bodyData))
```

这个操作：
- 创建一个全新的对象，与原始对象完全独立
- 移除所有的属性描述符限制
- 解除对象冻结状态
- 确保对象可以正常序列化

### 错误处理最佳实践

1. **分层错误处理**：
   - 技术错误：记录到控制台，供开发者调试
   - 用户错误：显示友好的多语言提示

2. **错误信息分类**：
   - 网络错误：显示网络相关提示
   - 权限错误：显示权限相关提示
   - 系统错误：显示通用系统错误提示

3. **防御性编程**：
   - 对所有外部数据进行验证
   - 对可能失败的操作进行异常捕获
   - 提供合理的默认值和降级方案

## 测试建议

### 1. 基础功能测试
- 正常的 WebSocket 通信
- 错误情况下的用户提示
- 多语言错误信息显示

### 2. 边界情况测试
- 传递冻结对象给 `webSocketSend`
- 传递包含循环引用的对象
- 传递非常大的对象
- 网络异常情况

### 3. 用户体验测试
- 确保用户不再看到 JavaScript 错误
- 错误提示信息清晰易懂
- 错误恢复机制正常工作

## 预期效果

修复后，用户将不再看到类似以下的技术性错误信息：
```
can't assign to property "time" on "{"time":1761360455,"auth_token":"cn0001c48AikL0JdY9q8LpvtPIg0YvcWODn0"}": not an object
```

取而代之的是友好的多语言错误提示：
```
当前未登录，需要登录才能进行此操作 (中文)
Current not logged in, need to login to perform this operation (English)
```

同时，技术性的错误信息仍然会记录在浏览器控制台中，便于开发者调试和问题排查。
