/**
 * Tab 管理工具
 * 用于管理多个 Tab 之间的 sessid 共享
 */
import { encryption, decryption } from '@/utils/jiami'

const TAB_COUNT_KEY = 'miru_tab_count'
const SESSID_KEY = 'miru_shared_sessid'
const ACCOUNT_KEY = 'miru_shared_account'
const PASSWORD_KEY = 'miru_shared_password'
const TAB_ID_KEY = 'miru_tab_id'
const LOGOUT_EVENT_KEY = 'miru_logout_event'


class TabManager {
  constructor() {
    this.tabId = this.generateTabId()
    this.isInitialized = false
    this.logoutCallbacks = []
    this.loginCallbacks = []
    this.init()
  }

  /**
   * 生成唯一的 Tab ID
   */
  generateTabId() {
    return 'tab_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 初始化 Tab 管理
   */
  init() {
    if (this.isInitialized) return

    // 将当前 Tab ID 存储到 sessionStorage
    sessionStorage.setItem(TAB_ID_KEY, this.tabId)

    // 增加 Tab 计数
    this.incrementTabCount()

    // 监听页面关闭事件
    this.setupBeforeUnloadListener()

    // 监听 localStorage 变化（用于 Tab 间通信）
    this.setupStorageListener()

    this.isInitialized = true
    console.log('TabManager initialized, Tab ID:', this.tabId)
  }

  /**
   * 增加 Tab 计数
   */
  incrementTabCount() {
    const currentCount = parseInt(localStorage.getItem(TAB_COUNT_KEY) || '0')
    localStorage.setItem(TAB_COUNT_KEY, (currentCount + 1).toString())
    console.log('Tab count incremented to:', currentCount + 1)
  }

  /**
   * 减少 Tab 计数
   */
  decrementTabCount() {
    const currentCount = parseInt(localStorage.getItem(TAB_COUNT_KEY) || '0')
    const newCount = Math.max(0, currentCount - 1)
    localStorage.setItem(TAB_COUNT_KEY, newCount.toString())
    console.log('Tab count decremented to:', newCount)
    return newCount
  }

  /**
   * 获取当前 Tab 数量
   */
  getTabCount() {
    return parseInt(localStorage.getItem(TAB_COUNT_KEY) || '0')
  }

  /**
   * 保存共享的 sessid 和 account
   */
  setSessid(sessid, account) {
    if (sessid) {
      const currentSessid = localStorage.getItem(SESSID_KEY)
      if (currentSessid !== sessid) {
        localStorage.setItem(SESSID_KEY, sessid)
      }
    }
    if (account) {
      const encrypedAccount = encryption(account)
      const currentAccount = localStorage.getItem(ACCOUNT_KEY)
      if (currentAccount !== encrypedAccount) {
        localStorage.setItem(ACCOUNT_KEY, encrypedAccount)
        localStorage.setItem(PASSWORD_KEY, localStorage.lanPassword)
      }
    }
  }

  /**
   * 获取共享的 sessid
   */
  getSessid() {
    return localStorage.getItem(SESSID_KEY)
  }

  /**
   * 获取共享的 account
   */
  getAccount() {
    const account = localStorage.getItem(ACCOUNT_KEY)
    if (!account) return ''
    return decryption(localStorage.getItem(ACCOUNT_KEY))
  }

  /**
   * 获取密码
   */
  getPassword() {
    const password = localStorage.getItem(PASSWORD_KEY)
    return password
  }

  /**
   * 清除共享的 sessid 和 account
   */
  clearSessid(triggerLogoutEvent = true) {
    localStorage.removeItem(SESSID_KEY)
    localStorage.removeItem(ACCOUNT_KEY)
    console.log('Sessid and account cleared from localStorage')

    // 触发登出事件，通知其他 Tab
    if (triggerLogoutEvent) {
      this.triggerLogoutEvent()
    }
  }

  /**
   * 检查是否有有效的 sessid 和 account
   */
  hasValidSessid() {
    const sessid = this.getSessid()
    const account = this.getAccount()
    return sessid && sessid.trim() !== '' && account && account.trim() !== ''
  }

  /**
   * 设置页面关闭监听器
   */
  setupBeforeUnloadListener() {
    window.addEventListener('beforeunload', () => {
      const remainingTabs = this.decrementTabCount()

      // 如果这是最后一个 Tab，清除 sessid
      if (remainingTabs === 0) {
        // 页面关闭时不触发登出事件，因为这是正常的关闭行为
        this.clearSessid(false)
        console.log('Last tab closing, sessid cleared')
      }
    })
  }

  /**
   * 设置 localStorage 变化监听器（用于 Tab 间通信）
   */
  setupStorageListener() {
    window.addEventListener('storage', (e) => {
      // 监听 sessid 变化，可以用于同步登录状态
      if (e.key === SESSID_KEY) {
        console.log('Sessid changed in another tab:', e.newValue)
        // 如果 sessid 被清除，说明有 Tab 执行了登出操作
        if (!e.newValue) {
          console.log('Sessid cleared in another tab, triggering logout')
          this.handleLogoutEvent()
        } else if (e.oldValue !== e.newValue) {
          // 如果 sessid 从无到有，或者值发生变化，说明有新的登录
          console.log('New sessid detected in another tab, triggering login sync')
          this.handleLoginEvent(e.newValue)
        }
      }

      // 监听登出事件
      if (e.key === LOGOUT_EVENT_KEY && e.newValue) {
        console.log('Logout event received from another tab')
        this.handleLogoutEvent()
      }
    })
  }

  /**
   * sessid 变化回调（可以被重写）
   */
  onSessidChanged(newSessid) {
    // 子类可以重写这个方法来处理 sessid 变化
    console.log('Sessid changed:', newSessid)
  }

  /**
   * 触发登出事件
   */
  triggerLogoutEvent() {
    const eventData = {
      tabId: this.tabId,
      timestamp: Date.now()
    }
    localStorage.setItem(LOGOUT_EVENT_KEY, JSON.stringify(eventData))
    console.log('Logout event triggered by tab:', this.tabId)

    // 立即清除事件标记，避免重复触发
    setTimeout(() => {
      localStorage.removeItem(LOGOUT_EVENT_KEY)
    }, 100)
  }

  /**
   * 处理登出事件
   */
  handleLogoutEvent() {
    // 执行所有注册的登出回调
    this.logoutCallbacks.forEach(callback => {
      try {
        callback()
      } catch (error) {
        console.error('Error executing logout callback:', error)
      }
    })
  }

  /**
   * 处理登录事件
   */
  handleLoginEvent(newSessid) {
    // 执行所有注册的登录回调
    this.loginCallbacks.forEach(callback => {
      try {
        callback(newSessid)
      } catch (error) {
        console.error('Error executing login callback:', error)
      }
    })
  }

  /**
   * 注册登出回调函数
   */
  onLogout(callback) {
    if (typeof callback === 'function') {
      this.logoutCallbacks.push(callback)
    }
  }

  /**
   * 移除登出回调函数
   */
  offLogout(callback) {
    const index = this.logoutCallbacks.indexOf(callback)
    if (index > -1) {
      this.logoutCallbacks.splice(index, 1)
    }
  }

  /**
   * 注册登录回调函数
   */
  onLogin(callback) {
    if (typeof callback === 'function') {
      this.loginCallbacks.push(callback)
    }
  }

  /**
   * 移除登录回调函数
   */
  offLogin(callback) {
    const index = this.loginCallbacks.indexOf(callback)
    if (index > -1) {
      this.loginCallbacks.splice(index, 1)
    }
  }

  /**
   * 获取当前 Tab ID
   */
  getTabId() {
    return this.tabId
  }

  /**
   * 检查是否是第一个 Tab
   */
  isFirstTab() {
    return this.getTabCount() === 1
  }

  /**
   * 销毁 Tab 管理器（手动调用）
   */
  destroy() {
    const remainingTabs = this.decrementTabCount()
    if (remainingTabs === 0) {
      // 手动销毁时不触发登出事件，因为这通常是页面关闭
      this.clearSessid(false)
    }
  }
}

// 创建单例实例
const tabManager = new TabManager()

export default tabManager
